@extends('admin.guard.template')
@section('content')
  <div class="card">
    <div class="card-body login-card-body">
      <p class="login-box-msg">２段階認証</p>
      <form method="post">
        @csrf
        <div class="form-group">
          <input type="text" class="form-control @error('two_factor_authentication_code') is-invalid @enderror"
            id="two-factor-authentication-code" placeholder="認証コード" name="two_factor_authentication_code" maxlength="6"
            value="{{ old('two_factor_authentication_code') }}" required>
        </div>
        @if (\Session::has('fail'))
          <div class="alert alert-danger font-weight-bold" style="font-size: 10px;">
            {{ Session::get('fail') }}
          </div>
        @endif
        <div class="col-6">
          <button type="submit" class="btn btn-primary btn-block">認証する</button>
        </div>
        <p class="mb-1 mt-3"><a href="{{ route('admin.guard.resending') }}">コードの再送信</a></p>
      </form>
    </div>
  </div>
@endsection
