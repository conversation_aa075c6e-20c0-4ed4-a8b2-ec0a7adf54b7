<?php

namespace App\Services;

use App\Exceptions\BusinessLogicException;
use Illuminate\Support\Facades\Mail;

class TwoFactorAuthenticationService
{
  private $sessionKeyLoginId;
  private $sessionKeyCode;
  private $sessionKeyTimeLimit;

  /**
   * コンストラクタ
   */
  public function __construct()
  {
    $this->sessionKeyLoginId = 'guard.id.admin';
    $this->sessionKeyCode = 'guard.code.admin';
    $this->sessionKeyTimeLimit = 'guard.time_limit.admin';
  }

  /**
   * セッション保持 確認.
   *
   * @return bool
   */
  public function hasSession()
  {
    $request = request();

    return $request->session()->has($this->sessionKeyTimeLimit) &&
           $request->session()->has($this->sessionKeyCode) &&
           $request->session()->has($this->sessionKeyLoginId);
  }

  /**
   * 認証コードを送信
   */
  public function sendMail($email = null, $loginId = null)
  {
    $currentRequest = request();
    if (!empty($email) && !empty($loginId)) {
      $code = $this->getCode();

      // セッション保存
      $sessionData = [
        $this->sessionKeyCode => $code,
        $this->sessionKeyTimeLimit => now()->addMinutes(30),
        $sessionData[$this->sessionKeyLoginId] = $loginId,
      ];

      $currentRequest->session()->put($sessionData);

      // メール送信
      Mail::send([], [], function ($message) use ($email, $code) {
        $body = "様\nこの度は、ご利用ありがとうございます。\n二段階認証のコードをお送りします。\n"
          . "--------------------------------------------------\n"
          . '認証コード：' . $code . "\n"
          . "--------------------------------------------------\n"
          . 'このコードは30分間有効です。';

        $message->to($email)
          ->subject('【HESTA LIFE】二段階認証コード')
          ->setBody($body);
      });
    } else {
      throw new BusinessLogicException('不正なアクセスです。');
    }
  }

  /**
   * コードの再送信
   */
  public function resendingMail()
  {
    // セッション確認
    if (!$this->hasSession()) {
      throw new BusinessLogicException('不正なアクセスです。ログインからやり直してください。');
    }

    // 一時保存された認証データを取得
    $authData = request()->session()->get('temp_auth_data');
    if (!$authData || !isset($authData['email']) || !isset($authData['id'])) {
      throw new BusinessLogicException('認証データが見つかりません。ログインからやり直してください。');
    }

    $this->sendMail($authData['email'], $authData['id']);
  }

  /**
   * ログインを試みる.
   */
  public function attempt($request)
  {
    // セッション確認
    if (!$this->hasSession()) {
      throw new BusinessLogicException('不正なアクセスです。ログインからやり直してください。');
    }

    // 認証コードの使用期限を超えている場合
    if ($request->session()->get($this->sessionKeyTimeLimit) <= now()) {
      throw new BusinessLogicException('認証コードの使用期限切れです。再度コードを発行してください。');
    }
    // 認証コードが違う場合
    if ($request->session()->get($this->sessionKeyCode) !== $request->input('two_factor_authentication_code')) {
      throw new BusinessLogicException('認証コードが違います。');
    }

    $userData = $request->session()->get('temp_auth_data');
    if (!$userData) {
      throw new BusinessLogicException('認証データが見つかりません。ログインからやり直してください。');
    }

    // 認証データをセッションに保存
    $request->session()->put('auth', $userData);

    // 認証成功後、2FAセッションをクリア
    $this->clearSession();

    return true;
  }

  /**
   * 2FA用のセッションをクリア.
   */
  public function clearSession()
  {
    $request = request();
    $request->session()->forget([
      $this->sessionKeyLoginId,
      $this->sessionKeyCode,
      $this->sessionKeyTimeLimit,
      'temp_auth_data',
    ]);
  }

  /**
   * 2FA プロセスを開始.
   */
  public function startTwoFactorAuthentication($authData)
  {
    // 認証データを一時的にセッションに保存
    request()->session()->put('temp_auth_data', $authData);
    $this->sendMail($authData['email'], $authData['user_id'] ?? null);
  }

  /**
   * 6桁数字 コード発行.
   * @return int
   */
  private function getCode()
  {
    return str_pad(rand(0, 999999), 6, 0, STR_PAD_LEFT);
  }
}
