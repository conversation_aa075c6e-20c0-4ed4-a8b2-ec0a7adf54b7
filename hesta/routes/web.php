<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Route::get('/', 'Welcome@getIndex');

/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
*/
Route::get('/healthcheck', function () {
  return 'health ok';
});
Route::get('/', function () {
  return redirect('admin/guard/login');
});
Route::match(['get', 'post'], 'admin/guard/login', 'Admin\Guard@actionLogin');
Route::get('admin/guard/logout', 'Admin\Guard@getLogout');
Route::get('admin/guard/two_factor_authentication', 'Admin\Guard@getTwoFactorAuthentication')->name('admin.guard.two_factor_authentication');
Route::post('admin/guard/two_factor_authentication', 'Admin\Guard@postTwoFactorAuthentication');
Route::get('admin/guard/resending', 'Admin\Guard@getResending')->name('admin.guard.resending');
Route::match(['get', 'post'], 'admin/guard/forgetpassword', 'Admin\Guard@actionForgetPassword');
Route::match(['get', 'post'], 'admin/guard/resetpassword', 'Admin\Guard@actionResetPassword');

Route::group(['prefix' => 'admin'], function () {
  // Admin Dashboard
  Route::get('standard/home', 'Admin\Standard\Home@getIndex');

  Route::get('standard/user/list', 'Admin\Standard\User@getList');
  Route::get('standard/user/detail/{id}', 'Admin\Standard\User@getDetail');
  Route::post('standard/user/detail/{id}', 'Admin\Standard\User@postDetail');
  Route::delete('standard/user/detail/{id}', 'Admin\Standard\User@deleteDetail');
  Route::get('standard/user/new', 'Admin\Standard\User@getNew');
  Route::post('standard/user/new', 'Admin\Standard\User@postNew');
  Route::get('standard/user/password', 'Admin\Standard\User@getPassword');
  Route::post('standard/user/password', 'Admin\Standard\User@postPassword');
  Route::get('standard/user/scan', 'Admin\Standard\User@getScan');

  Route::get('standard/user/point/list/{id}', 'Admin\Standard\User\Point@getList');
  Route::get('standard/user/point/new/{id}', 'Admin\Standard\User\Point@getNew');
  Route::post('standard/user/point/new/{id}', 'Admin\Standard\User\Point@postNew');

  Route::get('standard/user/star/list/{id}', 'Admin\Standard\User\Star@getList');
  Route::get('standard/user/star/new/{id}', 'Admin\Standard\User\Star@getNew');
  Route::post('standard/user/star/new/{id}', 'Admin\Standard\User\Star@postNew');
  Route::post('standard/user/star/rank/{id}', 'Admin\Standard\User\Star@postRank');

  Route::get('standard/brand/list', 'Admin\Standard\Brand@getList');
  Route::get('standard/brand/new', 'Admin\Standard\Brand@getNew');
  Route::post('standard/brand/new', 'Admin\Standard\Brand@postNew');
  Route::get('standard/brand/detail/{id}', 'Admin\Standard\Brand@getDetail');
  Route::post('standard/brand/detail/{id}', 'Admin\Standard\Brand@postDetail');

  Route::get('standard/brand/service/list/{id}', 'Admin\Standard\Brand\Service@getList');
  Route::get('standard/brand/service/new/{id}', 'Admin\Standard\Brand\Service@getNew');
  Route::post('standard/brand/service/new/{id}', 'Admin\Standard\Brand\Service@postNew');
  Route::get('standard/brand/service/detail/{id}', 'Admin\Standard\Brand\Service@getDetail');
  Route::post('standard/brand/service/detail/{id}', 'Admin\Standard\Brand\Service@postDetail');
  Route::delete('standard/brand/service/detail/{id}', 'Admin\Standard\Brand\Service@deleteDetail');

  Route::get('standard/news/list', 'Admin\Standard\News@getList');
  Route::get('standard/news/new', 'Admin\Standard\News@getNew');
  Route::post('standard/news/new', 'Admin\Standard\News@postNew');
  Route::get('standard/news/detail/{id}', 'Admin\Standard\News@getDetail');
  Route::post('standard/news/detail/{id}', 'Admin\Standard\News@postDetail');
  Route::delete('standard/news/detail/{id}', 'Admin\Standard\News@deleteDetail');

  Route::get('standard/report/list', 'Admin\Standard\Report@getList');
  Route::get('standard/report/summary', 'Admin\Standard\Report@getSummary');

  Route::get('standard/setting/list', 'Admin\Standard\Setting@getList');
  Route::get('standard/setting/new', 'Admin\Standard\Setting@getNew');
  Route::post('standard/setting/new', 'Admin\Standard\Setting@postNew');
  Route::get('standard/setting/detail/{id}', 'Admin\Standard\Setting@getDetail');
  Route::post('standard/setting/detail/{id}', 'Admin\Standard\Setting@postDetail');
  Route::delete('standard/setting/detail/{id}', 'Admin\Standard\Setting@deleteDetail');

  Route::get('standard/brand/setting/list/{id}', 'Admin\Standard\Brand@getListSetting');
});
